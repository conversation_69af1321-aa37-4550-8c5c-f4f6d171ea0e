//+------------------------------------------------------------------+
//|                                                  LongRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "base/BaseRegistry.mqh"
#include "mql4-lib-master/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| Long 類型註冊器                                                 |
//| 專門用於管理 long 類型數據的註冊器                              |
//+------------------------------------------------------------------+
class LongRegistry : public BaseRegistry
{
private:
    HashMap<int, long> m_registeredItems;    // 已註冊的 long 項目

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    LongRegistry(string name = "LongRegistry",
                string type = "LongRegistry",
                int maxRegistrations = 50,
                bool owned = true)
        : BaseRegistry(name, type, maxRegistrations, owned),
          m_registeredItems(NULL, false)
    {
        UpdateResult(true, "Long 註冊器構造完成", ERROR_LEVEL_INFO);
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~LongRegistry()
    {
        Clear();
    }

    //+------------------------------------------------------------------+
    //| 註冊 long 值                                                    |
    //+------------------------------------------------------------------+
    bool Register(int key, long value)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(!CheckMaxRegistrations())
        {
            return false;
        }

        // 驗證鍵值
        if(!ValidateKey(key))
        {
            UpdateResult(false, StringFormat("無效的鍵值: %d", key), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        if(m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 已經註冊", key), ERROR_LEVEL_WARNING);
            return false;
        }

        // 註冊項目
        m_registeredItems.set(key, value);

        UpdateResult(true, StringFormat("成功註冊 long 值: 鍵=%d, 值=%d", key, value), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 獲取已註冊的 long 值                                            |
    //+------------------------------------------------------------------+
    long GetRegisteredValue(int key, long defaultValue = 0)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 未註冊", key), ERROR_LEVEL_WARNING);
            return defaultValue;
        }

        long value = m_registeredItems.get(key, defaultValue);
        UpdateResult(true, StringFormat("成功獲取 long 值: 鍵=%d, 值=%d", key, value), ERROR_LEVEL_INFO);
        return value;
    }

    //+------------------------------------------------------------------+
    //| 更新已註冊的 long 值                                            |
    //+------------------------------------------------------------------+
    bool UpdateRegisteredValue(int key, long newValue)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已註冊
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 未註冊，無法更新", key), ERROR_LEVEL_ERROR);
            return false;
        }

        // 更新值
        m_registeredItems.set(key, newValue);

        UpdateResult(true, StringFormat("成功更新 long 值: 鍵=%d, 新值=%d", key, newValue), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 實現抽象方法                                                     |
    //+------------------------------------------------------------------+
    
    // 檢查指定鍵是否已註冊
    virtual bool IsRegistered(int key)
    {
        return m_registeredItems.contains(key);
    }
    
    // 取消註冊指定鍵
    virtual bool Unregister(int key)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 未註冊，無法取消註冊", key), ERROR_LEVEL_WARNING);
            return false;
        }

        bool result = m_registeredItems.remove(key);
        if(result)
        {
            UpdateResult(true, StringFormat("成功取消註冊鍵: %d", key), ERROR_LEVEL_INFO);
        }
        else
        {
            UpdateResult(false, StringFormat("取消註冊鍵 %d 失敗", key), ERROR_LEVEL_ERROR);
        }

        return result;
    }
    
    // 清理所有註冊項目
    virtual void Clear()
    {
        int totalCleared = GetRegisteredCount();
        m_registeredItems.clear();
        
        UpdateResult(true, StringFormat("已清理 %d 個 long 註冊項目", totalCleared), ERROR_LEVEL_INFO);
    }
    
    // 獲取已註冊項目數量
    virtual int GetRegisteredCount()
    {
        return m_registeredItems.size();
    }

    //+------------------------------------------------------------------+
    //| 專用方法                                                         |
    //+------------------------------------------------------------------+
    
    // 獲取所有已註冊的鍵
    int GetAllKeys(int &keys[])
    {
        int count = 0;
        ArrayResize(keys, GetRegisteredCount());
        
        foreachm(int, key, long, value, m_registeredItems)
        {
            keys[count] = key;
            count++;
        }
        
        return count;
    }
    
    // 獲取指定範圍內的值
    int GetValuesInRange(long minValue, long maxValue, int &keys[], long &values[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();
        
        ArrayResize(keys, totalCount);
        ArrayResize(values, totalCount);
        
        foreachm(int, key, long, value, m_registeredItems)
        {
            if(value >= minValue && value <= maxValue)
            {
                keys[count] = key;
                values[count] = value;
                count++;
            }
        }
        
        // 調整數組大小到實際數量
        ArrayResize(keys, count);
        ArrayResize(values, count);
        
        UpdateResult(true, StringFormat("找到 %d 個在範圍 [%d, %d] 內的值", count, minValue, maxValue), ERROR_LEVEL_INFO);
        return count;
    }
    
    // 計算所有註冊值的總和
    long CalculateSum()
    {
        long sum = 0;
        
        foreachm(int, key, long, value, m_registeredItems)
        {
            sum += value;
        }
        
        UpdateResult(true, StringFormat("計算總和完成: %d", sum), ERROR_LEVEL_INFO);
        return sum;
    }
    
    // 查找最大值
    long FindMaxValue(int &maxKey)
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法查找最大值", ERROR_LEVEL_WARNING);
            maxKey = -1;
            return 0;
        }
        
        long maxValue = LONG_MIN;
        maxKey = -1;
        
        foreachm(int, key, long, value, m_registeredItems)
        {
            if(value > maxValue)
            {
                maxValue = value;
                maxKey = key;
            }
        }
        
        UpdateResult(true, StringFormat("找到最大值: 鍵=%d, 值=%d", maxKey, maxValue), ERROR_LEVEL_INFO);
        return maxValue;
    }
    
    // 查找最小值
    long FindMinValue(int &minKey)
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法查找最小值", ERROR_LEVEL_WARNING);
            minKey = -1;
            return 0;
        }
        
        long minValue = LONG_MAX;
        minKey = -1;
        
        foreachm(int, key, long, value, m_registeredItems)
        {
            if(value < minValue)
            {
                minValue = value;
                minKey = key;
            }
        }
        
        UpdateResult(true, StringFormat("找到最小值: 鍵=%d, 值=%d", minKey, minValue), ERROR_LEVEL_INFO);
        return minValue;
    }
};

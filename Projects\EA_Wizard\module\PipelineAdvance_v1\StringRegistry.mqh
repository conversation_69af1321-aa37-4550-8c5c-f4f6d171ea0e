//+------------------------------------------------------------------+
//|                                                StringRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "base/BaseRegistry.mqh"
#include "mql4-lib-master/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| String 類型註冊器                                               |
//| 專門用於管理 string 類型數據的註冊器                            |
//+------------------------------------------------------------------+
class StringRegistry : public BaseRegistry
{
private:
    HashMap<int, string> m_registeredItems;    // 已註冊的 string 項目
    bool m_caseSensitive;                      // 是否區分大小寫
    int m_maxStringLength;                     // 最大字符串長度

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    StringRegistry(string name = "StringRegistry",
                  string type = "StringRegistry",
                  int maxRegistrations = 50,
                  bool owned = true,
                  bool caseSensitive = true,
                  int maxStringLength = 1024)
        : BaseRegistry(name, type, maxRegistrations, owned),
          m_registeredItems(NULL, false),
          m_caseSensitive(caseSensitive),
          m_maxStringLength(maxStringLength)
    {
        UpdateResult(true, "String 註冊器構造完成", ERROR_LEVEL_INFO);
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~StringRegistry()
    {
        Clear();
    }

    //+------------------------------------------------------------------+
    //| 註冊 string 值                                                  |
    //+------------------------------------------------------------------+
    bool Register(int key, string value)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(!CheckMaxRegistrations())
        {
            return false;
        }

        // 驗證鍵值
        if(!ValidateKey(key))
        {
            UpdateResult(false, StringFormat("無效的鍵值: %d", key), ERROR_LEVEL_ERROR);
            return false;
        }

        // 驗證字符串值
        if(!ValidateStringValue(value))
        {
            UpdateResult(false, StringFormat("無效的字符串值: %s", value), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        if(m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 已經註冊", key), ERROR_LEVEL_WARNING);
            return false;
        }

        // 處理大小寫敏感性
        string processedValue = m_caseSensitive ? value : StringToLower(value);

        // 註冊項目
        m_registeredItems.set(key, processedValue);

        UpdateResult(true, StringFormat("成功註冊 string 值: 鍵=%d, 值='%s'", key, processedValue), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 獲取已註冊的 string 值                                          |
    //+------------------------------------------------------------------+
    string GetRegisteredValue(int key, string defaultValue = "")
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 未註冊", key), ERROR_LEVEL_WARNING);
            return defaultValue;
        }

        string value = m_registeredItems.get(key, defaultValue);
        UpdateResult(true, StringFormat("成功獲取 string 值: 鍵=%d, 值='%s'", key, value), ERROR_LEVEL_INFO);
        return value;
    }

    //+------------------------------------------------------------------+
    //| 更新已註冊的 string 值                                          |
    //+------------------------------------------------------------------+
    bool UpdateRegisteredValue(int key, string newValue)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 驗證字符串值
        if(!ValidateStringValue(newValue))
        {
            UpdateResult(false, StringFormat("無效的字符串值: %s", newValue), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已註冊
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 未註冊，無法更新", key), ERROR_LEVEL_ERROR);
            return false;
        }

        // 處理大小寫敏感性
        string processedValue = m_caseSensitive ? newValue : StringToLower(newValue);

        // 更新值
        m_registeredItems.set(key, processedValue);

        UpdateResult(true, StringFormat("成功更新 string 值: 鍵=%d, 新值='%s'", key, processedValue), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 實現抽象方法                                                     |
    //+------------------------------------------------------------------+
    
    // 檢查指定鍵是否已註冊
    virtual bool IsRegistered(int key)
    {
        return m_registeredItems.contains(key);
    }
    
    // 取消註冊指定鍵
    virtual bool Unregister(int key)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 %d 未註冊，無法取消註冊", key), ERROR_LEVEL_WARNING);
            return false;
        }

        bool result = m_registeredItems.remove(key);
        if(result)
        {
            UpdateResult(true, StringFormat("成功取消註冊鍵: %d", key), ERROR_LEVEL_INFO);
        }
        else
        {
            UpdateResult(false, StringFormat("取消註冊鍵 %d 失敗", key), ERROR_LEVEL_ERROR);
        }

        return result;
    }
    
    // 清理所有註冊項目
    virtual void Clear()
    {
        int totalCleared = GetRegisteredCount();
        m_registeredItems.clear();
        
        UpdateResult(true, StringFormat("已清理 %d 個 string 註冊項目", totalCleared), ERROR_LEVEL_INFO);
    }
    
    // 獲取已註冊項目數量
    virtual int GetRegisteredCount()
    {
        return m_registeredItems.size();
    }

    //+------------------------------------------------------------------+
    //| 專用方法                                                         |
    //+------------------------------------------------------------------+
    
    // 設置大小寫敏感性
    void SetCaseSensitive(bool caseSensitive)
    {
        m_caseSensitive = caseSensitive;
        UpdateResult(true, StringFormat("大小寫敏感性已設置為: %s", caseSensitive ? "是" : "否"), ERROR_LEVEL_INFO);
    }
    
    // 獲取大小寫敏感性設置
    bool IsCaseSensitive()
    {
        return m_caseSensitive;
    }
    
    // 設置最大字符串長度
    void SetMaxStringLength(int maxLength)
    {
        m_maxStringLength = MathMax(maxLength, 1);
        UpdateResult(true, StringFormat("最大字符串長度已設置為: %d", m_maxStringLength), ERROR_LEVEL_INFO);
    }
    
    // 獲取最大字符串長度
    int GetMaxStringLength()
    {
        return m_maxStringLength;
    }
    
    // 獲取所有已註冊的鍵
    int GetAllKeys(int &keys[])
    {
        int count = 0;
        ArrayResize(keys, GetRegisteredCount());
        
        foreachm(int, key, string, value, m_registeredItems)
        {
            keys[count] = key;
            count++;
        }
        
        return count;
    }
    
    // 按字符串內容搜索
    int SearchByContent(string searchText, int &foundKeys[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();
        
        ArrayResize(foundKeys, totalCount);
        
        // 處理搜索文本的大小寫
        string processedSearchText = m_caseSensitive ? searchText : StringToLower(searchText);
        
        foreachm(int, key, string, value, m_registeredItems)
        {
            string processedValue = m_caseSensitive ? value : StringToLower(value);
            
            if(StringFind(processedValue, processedSearchText) >= 0)
            {
                foundKeys[count] = key;
                count++;
            }
        }
        
        // 調整數組大小到實際數量
        ArrayResize(foundKeys, count);
        
        UpdateResult(true, StringFormat("搜索 '%s' 找到 %d 個匹配項", searchText, count), ERROR_LEVEL_INFO);
        return count;
    }
    
    // 按前綴搜索
    int SearchByPrefix(string prefix, int &foundKeys[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();
        
        ArrayResize(foundKeys, totalCount);
        
        // 處理前綴的大小寫
        string processedPrefix = m_caseSensitive ? prefix : StringToLower(prefix);
        int prefixLength = StringLen(processedPrefix);
        
        foreachm(int, key, string, value, m_registeredItems)
        {
            string processedValue = m_caseSensitive ? value : StringToLower(value);
            
            if(StringLen(processedValue) >= prefixLength && 
               StringSubstr(processedValue, 0, prefixLength) == processedPrefix)
            {
                foundKeys[count] = key;
                count++;
            }
        }
        
        // 調整數組大小到實際數量
        ArrayResize(foundKeys, count);
        
        UpdateResult(true, StringFormat("前綴搜索 '%s' 找到 %d 個匹配項", prefix, count), ERROR_LEVEL_INFO);
        return count;
    }

protected:
    //+------------------------------------------------------------------+
    //| 受保護的輔助方法                                                 |
    //+------------------------------------------------------------------+
    
    // 驗證字符串值
    bool ValidateStringValue(string value)
    {
        // 檢查字符串長度
        if(StringLen(value) > m_maxStringLength)
        {
            return false;
        }
        
        return true;
    }
    
    // 將字符串轉換為小寫
    string StringToLower(string str)
    {
        string result = str;
        StringToLower(result);
        return result;
    }
};

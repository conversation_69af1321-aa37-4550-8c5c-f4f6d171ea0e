#property strict

#include "../../module/Pipeline/OnDeinitPipeline.mqh"

// 日誌記錄階段
class LoggingStage : public OnDeinitPipeline
{
public:
    bool Execute(int in = 0) override
    {
        // 獲取 EA 停止的原因
        string reason = "";
        
        switch(in)
        {
            case REASON_PROGRAM:
                reason = "程序停止";
                break;
            case REASON_REMOVE:
                reason = "EA 從圖表中移除";
                break;
            case REASON_RECOMPILE:
                reason = "EA 重新編譯";
                break;
            case REASON_CHARTCHANGE:
                reason = "圖表交易品種或週期改變";
                break;
            case REASON_CHARTCLOSE:
                reason = "圖表關閉";
                break;
            case REASON_PARAMETERS:
                reason = "輸入參數改變";
                break;
            case REASON_ACCOUNT:
                reason = "另一個賬戶啟用";
                break;
            case REASON_TEMPLATE:
                reason = "應用新模板";
                break;
            case REASON_INITFAILED:
                reason = "OnInit() 處理失敗";
                break;
            case REASON_CLOSE:
                reason = "終端關閉";
                break;
            default:
                reason = "未知原因";
                break;
        }
        
        // 記錄 EA 停止的原因
        Print("EA 停止，原因: ", reason, " (代碼: ", in, ")");
        
        // 記錄賬戶信息
        Print("賬戶餘額: ", AccountBalance());
        Print("賬戶淨值: ", AccountEquity());
        Print("賬戶利潤: ", AccountProfit());
        
        return true;
    }
};

#property strict

#include "../../module/PipelineAdvance_v1/MainPipeline.mqh"
#include "../../module/Trade/SignalHandler.mqh"

// 初始化單一交易信號處理器階段
class InitSignleHandler : public MainPipeline
{
public:
    InitSignleHandler() : MainPipeline(INIT_PARAMETERS, "InitSignleHandler"){}
    
    void Main() override { // 實現 Main 方法        
        // 創建單一交易信號處理器實例
        SignalHandler* single_handler = new SignalHandler();
        if(single_handler == NULL) {
            SetResult(false, "無法創建單一交易信號處理器實例");
            return;
        }
        
        // 註冊單一交易信號處理器
        if(!Register(single_handler, "SignalHandler", "單一交易信號處理器"))
        {
            SetResult(false, "單一交易信號處理器註冊失敗");
            return;
        }
    }
}init_signle_handler_stage;

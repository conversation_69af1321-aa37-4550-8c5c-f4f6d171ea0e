#property strict

#include "../../module/PipelineAdvance_v1/MainPipeline.mqh"

// 初始化檢查階段
class InitCheck : public MainPipeline
{
public:
    InitCheck() : MainPipeline(INIT_START, "InitCheck"){}
    void Main() override { // 實現 Main 方法
    
        int maxRetries = 4;
        int retryCount = 0;

        while((!IsTradeAllowed() || !IsConnected()) && retryCount < maxRetries) {
            if(!IsTradeAllowed())
                SetResult(false, "交易不允許");
            if(!IsConnected())
                SetResult(false, "未連接到交易伺服器");
            
            Sleep(5000);
            retryCount++;
        }

        if(retryCount >= maxRetries) {
            Alert("初始化檢查階段失敗，超過最大重試次數");
            SetResult(false, "初始化檢查階段失敗，超過最大重試次數");
        }
        else
        {
            SetResult(true, "初始化檢查階段成功");
        }
    }
}init_check_stage;

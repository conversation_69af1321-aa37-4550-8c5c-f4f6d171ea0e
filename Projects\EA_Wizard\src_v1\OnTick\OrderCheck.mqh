#property strict

#include "../../module/PipelineAdvance_v1/MainPipeline.mqh"
#include "../../module/mql4-lib-master/Trade/OrderTracker.mqh"

// 初始化訂單追蹤器階段
class OrderCheck : public MainPipeline
{
public:
    // 建構函數
    OrderCheck() : MainPipeline(TICK_DATA_FEED, "OrderCheck") {}
    void Main() override { // 實現 Main 方法        
        OrderTracker* order_tracker = GetPipelineObject(OrderTracker*, "OrderTracker");        
        if(order_tracker == NULL) {
            SetResult(false, "訂單追蹤器未初始化");            
            ExpertRemove();            
        }

        order_tracker.track();
    }
}order_check_stage;

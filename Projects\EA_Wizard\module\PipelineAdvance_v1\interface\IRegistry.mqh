//+------------------------------------------------------------------+
//|                                                     IRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingEvent.mqh"

//+------------------------------------------------------------------+
//| 註冊器模板介面                                                   |
//| 定義所有註冊器類型的共同操作契約，支持不同的 Key-Value 類型      |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
interface IRegistry
{
    //+------------------------------------------------------------------+
    //| 基本註冊操作                                                     |
    //+------------------------------------------------------------------+

    // 註冊 Key-Value 對
    bool Register(Key key, Val value);

    // 獲取已註冊的值
    Val GetRegisteredValue(Key key, Val defaultValue);

    // 更新已註冊的值
    bool UpdateRegisteredValue(Key key, Val newValue);

    // 檢查指定鍵是否已註冊
    bool IsRegistered(Key key);

    // 取消註冊指定鍵
    bool Unregister(Key key);

    // 清理所有註冊項目
    void Clear();

    //+------------------------------------------------------------------+
    //| 狀態查詢                                                         |
    //+------------------------------------------------------------------+

    // 獲取已註冊項目數量
    int GetRegisteredCount();

    // 獲取最大註冊數量
    int GetMaxRegistrations();

    // 檢查是否已滿
    bool IsFull();

    // 檢查是否為空
    bool IsEmpty();

    //+------------------------------------------------------------------+
    //| 狀態管理                                                         |
    //+------------------------------------------------------------------+

    // 設置啟用狀態
    void SetEnabled(bool enabled);

    // 檢查是否啟用
    bool IsEnabled();

    // 設置擁有狀態
    void SetOwned(bool owned);

    // 檢查是否擁有註冊項目
    bool IsOwned();

    //+------------------------------------------------------------------+
    //| 結果和信息                                                       |
    //+------------------------------------------------------------------+

    // 獲取執行結果
    PipelineResult* GetResult();

    // 獲取註冊器名稱
    string GetName();

    // 獲取註冊器類型
    string GetType();

    // 獲取註冊器狀態信息
    string GetStatusInfo();
};

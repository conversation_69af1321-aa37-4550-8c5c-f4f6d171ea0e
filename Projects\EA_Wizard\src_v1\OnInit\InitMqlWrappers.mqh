#property strict

#include "../../module/PipelineAdvance_v1/MainPipeline.mqh"
#include "../../module/Trade/MqlStruct.mqh"

// 初始化 MQL 封裝器階段
class InitMqlWrapper : public MainPipeline
{
public:
    InitMqlWrapper() : MainPipeline(INIT_VARIABLES, "InitMqlWrapper"){}
    
    void Main() override { // 實現 Execute 方法
        // 創建 MQL開倉 封裝器實例
        MqlOrderWrapper* mql_order_wrapper = new MqlOrderWrapper();
        if(mql_order_wrapper == NULL) {
            SetResult(false, "無法創建 MQL開倉 封裝器實例");            
            return;            
        }

        // 創建 MQL平倉 封裝器實例
        MqlCloseWrapper* mql_close_wrapper = new MqlCloseWrapper();
        if(mql_close_wrapper == NULL) {
            SetResult(false, "無法創建 MQL平倉 封裝器實例");            
            return;            
        }
        
        // 註冊 MQL開倉 封裝器
        if(!Register(mql_order_wrapper, "MqlOrderWrapper", "MQL開倉 封裝器"))
        {
            SetResult(false, "MQL開倉 封裝器註冊失敗");            
            return;            
        }

        // 註冊 MQL平倉 封裝器
        if(!Register(mql_close_wrapper, "MqlCloseWrapper", "MQL平倉 封裝器"))
        {
            SetResult(false, "MQL平倉 封裝器註冊失敗");            
            return;            
        }
    }
}init_mql_wrapper_stage;

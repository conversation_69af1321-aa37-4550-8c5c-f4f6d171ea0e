#property strict

template <typename T>
long GetAddress(T pointer)
{
return long(StringFormat("%I64d",pointer));
}
template <typename T>
int Hash(T value)
{
return (int)GetAddress(value);
}

//+------------------------------------------------------------------+
//| 簡單XOR對稱加密與解密函數                                       |
//+------------------------------------------------------------------+
string SimpleXOREncrypt(string text, string key)
{
   string result = "";
   int keyLen = StringLen(key);
   for(int i=0; i<StringLen(text); i++)
   {
      result += CharToStr(StringGetChar(text, i) ^ StringGetChar(key, i % keyLen));
   }
   return result;
}

string SimpleXORDecrypt(string cipher, string key)
{
   // XOR加密和解密過程相同
   return SimpleXOREncrypt(cipher, key);
}
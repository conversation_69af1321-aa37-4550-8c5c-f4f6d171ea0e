# Martingale 模組類別圖

## 概述

Martingale 模組提供馬丁格爾交易策略的訂單組管理功能，包含訂單組的創建、管理和操作。

## 類別關係圖

```mermaid
classDiagram
    class IMartingaleGroupManager {
        <<interface>>
        +SetGroup(string id, MartingaleOrderGroup* group) bool
        +RemoveGroup(string id) bool
        +GetGroup(string id) MartingaleOrderGroup*
        +CloseAllGroups() bool
        +UpdateAllGroups(int tickets[]) void
        +GetTotalProfit() double
        +GetGroupCount() int
        +GroupExists(string id) bool
        +ClearAllGroups() void
        +GetStatistics() string
        +SetAllGroupsTP(double profit) void
        +SetAllGroupsSL(double loss) void
    }

    class MartingaleGroupManager {
        -HashMap~string, MartingaleOrderGroup*~ m_groups
        +SetGroup(string id, MartingaleOrderGroup* group) bool
        +RemoveGroup(string id) bool
        +GetGroup(string id) MartingaleOrderGroup*
        +CloseAllGroups() bool
        +UpdateAllGroups(int tickets[]) void
        +GetTotalProfit() double
        +GetGroupCount() int
        +GroupExists(string id) bool
        +ClearAllGroups() void
        +GetStatistics() string
        +SetAllGroupsTP(double profit) void
        +SetAllGroupsSL(double loss) void
    }

    class MartingaleOrderGroup {
        -string m_groupId
        -ENUM_MARTINGALE_GROUP_TYPE m_groupType
        -ENUM_MARTINGALE_GROUP_STATUS m_groupStatus
        -bool m_isReversed
        -static string s_nextGroupId
        +MartingaleOrderGroup(string symbol, ENUM_MARTINGALE_GROUP_TYPE groupType, bool isReversed)
        +groupCurrentLevel() int
        +groupBreakEvenPrice() double
        +groupId() string
        +groupType() ENUM_MARTINGALE_GROUP_TYPE
        +groupStatus() ENUM_MARTINGALE_GROUP_STATUS
        +groupTypeString() string
        +groupLowestPrice() double
        +groupHighestPrice() double
        +groupPriceRange() double
        +groupOrderTotal() int
        +groupCommission() double
        +groupSwap() double
        +groupNetProfit() double
        +closeAll() bool
        +setStatus(ENUM_MARTINGALE_GROUP_STATUS status) void
        -generateGroupId() string
        -initializeIdCounter() void
    }

    class OrderGroup {
        <<from mql4-lib>>
        -FxSymbol* m_symbol
        -bool m_ownsSymbol
        +OrderGroup(string symbol)
        +OrderGroup(FxSymbol* symbol)
        +groupTakeProfit(double price) void
        +groupStopLoss(double price) void
        +groupDoubleProperty(OrderDoubleProperty func) double
        +groupAvg() double
        +groupProfit() double
        +groupLots() double
        +clearClosed() void
    }

    class Vector~int~ {
        <<from mql4-lib>>
        +size() int
        +get(int index) int
        +add(int value) void
        +remove(int index) void
    }

    class HashMap~string_MartingaleOrderGroup*~ {
        <<from mql4-lib>>
        +contains(string key) bool
        +set(string key, MartingaleOrderGroup* value) void
        +get(string key) MartingaleOrderGroup*
        +remove(string key) bool
    }

    class ENUM_MARTINGALE_GROUP_TYPE {
        <<enumeration>>
        MARTINGALE_TYPE_STANDARD
        MARTINGALE_TYPE_REVERSE
        MARTINGALE_TYPE_GRID
        MARTINGALE_TYPE_PROGRESSIVE
        MARTINGALE_TYPE_CUSTOM
    }

    class ENUM_MARTINGALE_GROUP_STATUS {
        <<enumeration>>
        MARTINGALE_STATUS_ACTIVE
        MARTINGALE_STATUS_INACTIVE
        MARTINGALE_STATUS_COMPLETED
        MARTINGALE_STATUS_CANCELLED
        MARTINGALE_STATUS_FULL
    }

    %% 繼承關係
    MartingaleGroupManager ..|> IMartingaleGroupManager : implements
    MartingaleOrderGroup --|> OrderGroup : extends
    OrderGroup --|> Vector~int~ : extends

    %% 組合關係
    MartingaleGroupManager *-- HashMap~string_MartingaleOrderGroup*~ : contains
    MartingaleGroupManager o-- MartingaleOrderGroup : manages
    MartingaleOrderGroup *-- ENUM_MARTINGALE_GROUP_TYPE : uses
    MartingaleOrderGroup *-- ENUM_MARTINGALE_GROUP_STATUS : uses

    %% 依賴關係
    IMartingaleGroupManager ..> MartingaleOrderGroup : depends on
```

## 核心類別說明

### IMartingaleGroupManager (介面)

馬丁格爾訂單組管理器的介面定義，提供統一的管理操作。

**主要職責：**

- 定義訂單組管理的標準介面
- 提供訂單組的 CRUD 操作
- 定義統計和批量操作方法

### MartingaleGroupManager (實現類)

馬丁格爾訂單組管理器的具體實現，負責管理多個馬丁格爾訂單組。

**主要職責：**

- 實現 IMartingaleGroupManager 介面
- 使用 HashMap 存儲和管理訂單組
- 提供訂單組的統計和批量操作功能
- 處理訂單組的生命週期管理

**核心成員：**

- `m_groups`: HashMap 容器，存儲訂單組

### MartingaleOrderGroup (核心類)

馬丁格爾訂單組類，繼承自 OrderGroup，專門處理馬丁格爾策略的訂單管理。

**主要職責：**

- 管理馬丁格爾策略的訂單集合
- 提供訂單組的統計計算功能
- 處理訂單組的狀態管理
- 提供價格分析和盈虧計算

**核心成員：**

- `m_groupId`: 訂單組唯一識別碼
- `m_groupType`: 馬丁格爾策略類型
- `m_groupStatus`: 訂單組當前狀態
- `m_isReversed`: 是否為反向馬丁格爾

## 枚舉類型

### ENUM_MARTINGALE_GROUP_TYPE

定義馬丁格爾策略的類型：

- `MARTINGALE_TYPE_STANDARD`: 標準馬丁格爾
- `MARTINGALE_TYPE_REVERSE`: 反向馬丁格爾
- `MARTINGALE_TYPE_GRID`: 網格馬丁格爾
- `MARTINGALE_TYPE_PROGRESSIVE`: 漸進式馬丁格爾
- `MARTINGALE_TYPE_CUSTOM`: 自定義馬丁格爾

### ENUM_MARTINGALE_GROUP_STATUS

定義訂單組的狀態：

- `MARTINGALE_STATUS_ACTIVE`: 活躍狀態
- `MARTINGALE_STATUS_INACTIVE`: 非活躍狀態
- `MARTINGALE_STATUS_COMPLETED`: 完成狀態
- `MARTINGALE_STATUS_CANCELLED`: 取消狀態
- `MARTINGALE_STATUS_FULL`: 已滿狀態

## 設計模式

### 1. 介面隔離原則 (Interface Segregation)

- `IMartingaleGroupManager` 提供清晰的介面定義
- 分離介面和實現，提高可測試性和可維護性

### 2. 繼承和組合

- `MartingaleOrderGroup` 繼承 `OrderGroup`，重用基礎功能
- `MartingaleGroupManager` 組合 `HashMap` 進行訂單組管理

### 3. 策略模式 (Strategy Pattern)

- 通過 `ENUM_MARTINGALE_GROUP_TYPE` 支援不同的馬丁格爾策略
- 可擴展支援新的策略類型

## 主要功能流程

### 訂單組管理流程

1. 創建 `MartingaleOrderGroup` 實例
2. 通過 `MartingaleGroupManager.SetGroup()` 添加到管理器
3. 使用各種查詢和操作方法管理訂單組
4. 通過 `RemoveGroup()` 或 `ClearAllGroups()` 清理資源

### 統計計算流程

1. 遍歷訂單組中的所有訂單
2. 計算各種統計指標（盈虧、價格範圍等）
3. 提供統一的統計介面供外部調用

## 依賴關係

- 依賴 `mql4-lib` 的 `HashMap`、`Vector`、`OrderGroup` 等基礎類
- 使用 MQL4 的訂單管理 API
- 依賴 `Order` 類進行訂單操作

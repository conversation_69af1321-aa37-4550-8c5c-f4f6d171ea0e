#property strict

#include "../../module/PipelineAdvance_v1/MainPipeline.mqh"
#include "../../module/mql4-lib-master/Trade/OrderManager.mqh"
#include "../../module/Trade/MqlStruct.mqh"
#include "../../module/Trade/SignalHandler.mqh"

// 初始化訂單追蹤器階段
class TradeExecution : public MainPipeline
{
public:
    // 建構函數
    TradeExecution() : MainPipeline(TICK_RISK_CONTROL, "TradeExecution") {}
    void Main() override { // 實現 Execute 方法
        // 獲取訂單管理器實例
        OrderManager* order_manager = GetPipelineObject(OrderManager*, "OrderManager");
        if(order_manager == NULL) {
            SetResult(false, "訂單管理器未初始化");
            ExpertRemove();
        }

        // 獲取單一交易信號處理器實例
        SignalHandler* signal_handler = GetPipelineObject(SignalHandler*, "SignalHandler");        
        if(signal_handler == NULL) {
            SetResult(false, "單一交易信號處理器未初始化");            
            ExpertRemove();            
        }

        // 獲取 MQL開倉 封裝器實例
        MqlOrderWrapper* mql_order_wrapper = GetPipelineObject(MqlOrderWrapper*, "MqlOrderWrapper");        
        if(mql_order_wrapper == NULL) {
            SetResult(false, "MQL開倉 封裝器未初始化");            
            ExpertRemove();            
        }

        // 獲取 MQL平倉 封裝器實例
        MqlCloseWrapper* mql_close_wrapper = GetPipelineObject(MqlCloseWrapper*, "MqlCloseWrapper");        
        if(mql_close_wrapper == NULL) {
            SetResult(false, "MQL平倉 封裝器未初始化");            
            ExpertRemove();            
        }

        MqlOrder order_config;
        MqlClose close_config;
        mql_order_wrapper.GetOrder(order_config);
        mql_close_wrapper.GetClose(close_config);

        // 如果有買入或賣出信號
        if(signal_handler.GetSignal() == SIGNAL_BUY || signal_handler.GetSignal() == SIGNAL_SELL){
            // 交易執行
            int ticket = order_manager.market(order_config.type, order_config.volume, order_config.stoploss, order_config.takeprofit, order_config.comment);
            if(ticket < 0)
            {
                Print("交易執行失敗");
            }
        }

        // 如果有平倉信號
        if(signal_handler.GetSignal() == SIGNAL_CLOSE)
        {
            // 平倉
            bool result = order_manager.close(close_config.ticket);
            if(!result)
            {
                Print("平倉失敗");
            }
        }
    }
}trade_execution_stage;
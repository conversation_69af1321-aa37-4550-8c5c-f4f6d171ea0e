#property strict

#include "../../module/PipelineAdvance_v1/MainPipeline.mqh"
#include "../../module/mql4-lib-master/Trade/OrderTracker.mqh"
#include "../../module/Trade/SignalHandler.mqh"

// 初始化訂單追蹤器階段
class SignalCheck : public MainPipeline
{
public:
    // 建構函數
    SignalCheck() : MainPipeline(TICK_SIGNAL_ANALYSIS, "SignalCheck") {}
    void Main() override { // 實現 Main 方法
        // 獲取訂單追蹤器實例
        OrderTracker* order_tracker = GetPipelineObject(OrderTracker*, "OrderTracker");
        if(order_tracker == NULL) {
            SetResult(false, "訂單追蹤器未初始化");
            ExpertRemove();
        }

        // 獲取單一交易信號處理器實例
        SignalHandler* signal_handler = GetPipelineObject(SignalHandler*, "SignalHandler");
        if(signal_handler == NULL) {
            SetResult(false, "單一交易信號處理器未初始化");
            ExpertRemove();
        }

        // 獲取所有訂單
        Vector<Order*>* orders = new Vector<Order*>();
        order_tracker.getOrders(orders);

        // 清除之前的信號
        signal_handler.SetSignal(SIGNAL_NONE);

        // 如果沒有訂單，則生成新的買入信號
        if(orders.isEmpty()){
            signal_handler.SetSignal(SIGNAL_BUY, Close[0], 1.0, "SignalCheck");
            Print("生成新的買入信號");
        }

        // 否則如果訂單巳經存在交易單庫超過10分鐘，則平倉信號
        else if(!orders.isEmpty()){
            Order* order = orders.get(0);
            if(order.getOpenTime() < TimeCurrent() - 600){
                signal_handler.SetSignal(SIGNAL_CLOSE, Close[0], 1.0, "SignalCheck");
                Print("生成新的平倉信號");
            }
        }
    }
}signal_check_stage;
